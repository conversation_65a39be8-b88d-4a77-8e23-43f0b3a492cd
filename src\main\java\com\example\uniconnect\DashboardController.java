package com.example.uniconnect;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.image.WritableImage;
import javafx.scene.image.PixelWriter;
import javafx.scene.paint.Color;
import javafx.animation.FadeTransition;
import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.util.Duration;
import javafx.scene.control.ScrollPane;
import javafx.scene.layout.GridPane;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.YearMonth;
import java.util.Locale;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.io.IOException;

public class DashboardController {
    @FXML
    private BorderPane dashboardContainer;

    @FXML
    private HBox header;

    @FXML
    private VBox sidebar;

    @FXML
    private VBox contentLeft;

    @FXML
    private VBox contentRight;

    @FXML
    private Label greetingLabel;

    @FXML
    private Label dateLabel;

    @FXML
    private ImageView profileImageView;

    @FXML
    private TextArea postTextArea;

    @FXML
    private ComboBox<String> categoryComboBox;

    @FXML
    private Button postButton;

    @FXML
    private Button logoutButton;

    @FXML
    private Button dashboardButton;

    @FXML
    private Button likeButton;

    @FXML
    private Button likeButton1;

    @FXML
    private Button likeButton2;

    @FXML
    private Button likeButton3;

    @FXML
    private ImageView postImageView;

    // Routine Toggle Components
    @FXML
    private Button classRoutineButton;

    @FXML
    private Button examRoutineButton;

    @FXML
    private VBox routineContentContainer;

    @FXML
    private VBox classRoutineContent;

    @FXML
    private VBox examRoutineContent;

    @FXML
    private ScrollPane classRoutineScrollPane;

    @FXML
    private ScrollPane examRoutineScrollPane;

    @FXML
    private VBox calendarContainer;

    @FXML
    private Label monthYearLabel;

    @FXML
    private GridPane calendarGrid;

    private boolean isLiked = false;
    private boolean isLiked1 = false;
    private boolean isLiked2 = false;
    private boolean isLiked3 = false;

    @FXML
    public void initialize() {
        // Set greeting to match reference design
        String userName = "Tommy"; // As shown in reference design
        if (Data.fullName != null && !Data.fullName.isEmpty()) {
            userName = Data.fullName;
        }

        // Set greeting to match reference design
        String greeting = "Good morning, " + userName;
        greetingLabel.setText(greeting);

        // Initialize dynamic date display
        initializeDateDisplay();

        // Initialize category ComboBox
        categoryComboBox.getItems().addAll(
            "Academic", "Buy & Sell", "Lost & Found", "Events", "General"
        );

        // Load statistics image
        loadStatisticsImage();

        // Initialize routine toggle (Class Routine selected by default)
        initializeRoutineToggle();

        // Initialize calendar component
        initializeCalendar();
    }

    private void initializeRoutineToggle() {
        // Ensure class routine is shown by default
        if (classRoutineScrollPane != null && examRoutineScrollPane != null) {
            classRoutineScrollPane.setVisible(true);
            classRoutineScrollPane.setManaged(true);
            examRoutineScrollPane.setVisible(false);
            examRoutineScrollPane.setManaged(false);

            System.out.println("Routine toggle initialized - Class Routine scroll pane active by default");
        } else {
            System.out.println("Warning: Routine scroll panes not found during initialization");
        }

        // Set initial button states with enhanced logic
        if (classRoutineButton != null && examRoutineButton != null) {
            // Clear any existing style classes first
            classRoutineButton.getStyleClass().removeAll("active");
            examRoutineButton.getStyleClass().removeAll("active");

            // Add active class to class routine button
            classRoutineButton.getStyleClass().add("active");

            // Force style refresh
            classRoutineButton.applyCss();
            examRoutineButton.applyCss();

            System.out.println("Button states initialized - Class Routine active (150px width)");
            System.out.println("Class button classes: " + classRoutineButton.getStyleClass());
            System.out.println("Exam button classes: " + examRoutineButton.getStyleClass());
        } else {
            System.out.println("Warning: Toggle buttons not found during initialization");
        }
    }

    private void loadStatisticsImage() {
        if (postImageView == null) {
            System.out.println("ImageView not found - cannot load image");
            return;
        }

        try {
            // First try to load the uniconnect.png since it's larger and more likely to work
            Image statisticsImage = null;

            // Try uniconnect.png first (it's 23KB so more likely to be valid)
            try {
                statisticsImage = new Image(getClass().getResourceAsStream("/com/example/uniconnect/images/uniconnect.png"));
                if (statisticsImage != null && !statisticsImage.isError()) {
                    System.out.println("Successfully loaded uniconnect.png as statistics image");
                    postImageView.setImage(statisticsImage);
                    return;
                }
            } catch (Exception e) {
                System.out.println("Failed to load uniconnect.png: " + e.getMessage());
            }

            // Try Statistics.jpg as backup
            try {
                statisticsImage = new Image(getClass().getResourceAsStream("/com/example/uniconnect/images/Statistics.jpg"));
                if (statisticsImage != null && !statisticsImage.isError()) {
                    System.out.println("Successfully loaded Statistics.jpg");
                    postImageView.setImage(statisticsImage);
                    return;
                }
            } catch (Exception e) {
                System.out.println("Failed to load Statistics.jpg: " + e.getMessage());
            }

            // If both fail, create a placeholder
            System.out.println("Creating placeholder image for statistics");
            Image placeholderImage = createPlaceholderImage();
            postImageView.setImage(placeholderImage);

        } catch (Exception e) {
            System.out.println("Error in loadStatisticsImage: " + e.getMessage());
            e.printStackTrace();

            // Last resort - create placeholder
            try {
                Image placeholderImage = createPlaceholderImage();
                postImageView.setImage(placeholderImage);
            } catch (Exception ex) {
                System.out.println("Failed to create placeholder image: " + ex.getMessage());
            }
        }
    }

    private Image createPlaceholderImage() {
        // Create a statistics-like chart as placeholder
        WritableImage placeholderImage = new WritableImage(400, 200);
        PixelWriter pixelWriter = placeholderImage.getPixelWriter();

        // Fill background with light gray
        for (int x = 0; x < 400; x++) {
            for (int y = 0; y < 200; y++) {
                pixelWriter.setColor(x, y, Color.color(0.95, 0.95, 0.95));
            }
        }

        // Draw simple bar chart-like pattern
        int[] barHeights = {60, 80, 120, 90, 110, 70, 100};
        int barWidth = 40;
        int spacing = 10;

        for (int i = 0; i < barHeights.length; i++) {
            int startX = 50 + i * (barWidth + spacing);
            int startY = 180 - barHeights[i];

            // Draw bars in blue color
            for (int x = startX; x < startX + barWidth && x < 400; x++) {
                for (int y = startY; y < 180 && y < 200; y++) {
                    pixelWriter.setColor(x, y, Color.color(0.2, 0.5, 0.8));
                }
            }
        }

        // Add title area (darker gray)
        for (int x = 0; x < 400; x++) {
            for (int y = 0; y < 30; y++) {
                pixelWriter.setColor(x, y, Color.color(0.8, 0.8, 0.8));
            }
        }

        return placeholderImage;
    }

    // Date and Calendar Methods
    private void initializeDateDisplay() {
        if (dateLabel != null) {
            // Format: "Day, DD Month, YYYY" (e.g., "Monday, 15 January, 2024")
            LocalDate today = LocalDate.now();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd MMMM, yyyy", Locale.ENGLISH);
            String formattedDate = today.format(formatter);
            dateLabel.setText(formattedDate);
            System.out.println("Date display initialized: " + formattedDate);
        } else {
            System.out.println("Warning: Date label not found during initialization");
        }
    }

    private void initializeCalendar() {
        if (calendarGrid == null || monthYearLabel == null) {
            System.out.println("Warning: Calendar components not found during initialization");
            return;
        }

        try {
            // Clear any existing content
            calendarGrid.getChildren().clear();
            calendarGrid.getRowConstraints().clear();
            calendarGrid.getColumnConstraints().clear();

            // Get current date
            LocalDate today = LocalDate.now();
            YearMonth currentMonth = YearMonth.from(today);

            // Set month/year label
            DateTimeFormatter monthYearFormatter = DateTimeFormatter.ofPattern("MMMM yyyy", Locale.ENGLISH);
            monthYearLabel.setText(currentMonth.format(monthYearFormatter));

            // Create calendar grid
            createCalendarGrid(currentMonth, today);

            System.out.println("Calendar initialized for: " + currentMonth.format(monthYearFormatter));
        } catch (Exception e) {
            System.out.println("Error initializing calendar: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void createCalendarGrid(YearMonth yearMonth, LocalDate today) {
        // Get first day of month and number of days
        LocalDate firstOfMonth = yearMonth.atDay(1);
        int daysInMonth = yearMonth.lengthOfMonth();
        int startDayOfWeek = firstOfMonth.getDayOfWeek().getValue() % 7; // Sunday = 0

        // Create calendar grid (6 rows x 7 columns max)
        int row = 0;
        int col = startDayOfWeek;

        for (int day = 1; day <= daysInMonth; day++) {
            LocalDate currentDate = yearMonth.atDay(day);
            Label dayLabel = new Label(String.valueOf(day));

            // Apply styling based on date
            if (currentDate.equals(today)) {
                // Today's date - light blue background
                dayLabel.getStyleClass().addAll("calendar-day", "calendar-today");
            } else {
                // Regular date - light gray background
                dayLabel.getStyleClass().addAll("calendar-day", "calendar-regular");
            }

            // Add to grid
            calendarGrid.add(dayLabel, col, row);

            // Move to next position
            col++;
            if (col > 6) {
                col = 0;
                row++;
            }
        }
    }

    // Routine Toggle Methods
    @FXML
    public void showClassRoutine() {
        // Update button states
        updateToggleButtonStates(true);

        // Switch content with animation
        switchRoutineContent(true);

        System.out.println("Switched to Class Routine");
    }

    @FXML
    public void showExamRoutine() {
        // Update button states
        updateToggleButtonStates(false);

        // Switch content with animation
        switchRoutineContent(false);

        System.out.println("Switched to Exam Routine");
    }

    private void updateToggleButtonStates(boolean showClassRoutine) {
        if (classRoutineButton == null || examRoutineButton == null) {
            System.out.println("Toggle buttons not found");
            return;
        }

        // Clear all active states first to ensure clean state
        classRoutineButton.getStyleClass().removeAll("active");
        examRoutineButton.getStyleClass().removeAll("active");

        // Force CSS refresh to clear any cached styles
        classRoutineButton.applyCss();
        examRoutineButton.applyCss();

        if (showClassRoutine) {
            // Class routine active
            classRoutineButton.getStyleClass().add("active");
            System.out.println("Class routine button activated");
            System.out.println("Class button final classes: " + classRoutineButton.getStyleClass());
        } else {
            // Exam routine active
            examRoutineButton.getStyleClass().add("active");
            System.out.println("Exam routine button activated");
            System.out.println("Exam button final classes: " + examRoutineButton.getStyleClass());
        }

        // Force another CSS refresh to apply new styles
        classRoutineButton.applyCss();
        examRoutineButton.applyCss();
    }

    private void switchRoutineContent(boolean showClassRoutine) {
        if (classRoutineScrollPane == null || examRoutineScrollPane == null) {
            System.out.println("Routine scroll panes not found");
            return;
        }

        // Create fade transition
        FadeTransition fadeOut = new FadeTransition(Duration.millis(200), routineContentContainer);
        fadeOut.setFromValue(1.0);
        fadeOut.setToValue(0.0);

        fadeOut.setOnFinished(e -> {
            // Switch content - now working with ScrollPanes
            if (showClassRoutine) {
                classRoutineScrollPane.setVisible(true);
                classRoutineScrollPane.setManaged(true);
                examRoutineScrollPane.setVisible(false);
                examRoutineScrollPane.setManaged(false);
                System.out.println("Switched to class routine scroll pane");
            } else {
                examRoutineScrollPane.setVisible(true);
                examRoutineScrollPane.setManaged(true);
                classRoutineScrollPane.setVisible(false);
                classRoutineScrollPane.setManaged(false);
                System.out.println("Switched to exam routine scroll pane");
            }

            // Fade in
            FadeTransition fadeIn = new FadeTransition(Duration.millis(300), routineContentContainer);
            fadeIn.setFromValue(0.0);
            fadeIn.setToValue(1.0);
            fadeIn.play();
        });

        fadeOut.play();
    }

    @FXML
    public void handlePost() {
        // This method would handle posting new content
        // For now, just print the post text and clear the input
        if (postTextArea.getText().isEmpty()) {
            return;
        }

        // Print the post text to console
        System.out.println("New post: " + postTextArea.getText());
        if (categoryComboBox.getValue() != null) {
            System.out.println("Category: " + categoryComboBox.getValue());
        }

        // Clear the input fields
        postTextArea.clear();
        categoryComboBox.setValue(null);
    }

    @FXML
    public void handleLogout() {
        try {
            // Clear user data
            Data.userId = 0;
            Data.studentId = null;
            Data.fullName = null;

            // Load login screen
            FXMLLoader loader = new FXMLLoader(getClass().getResource("LoginSignup.fxml"));
            Scene scene = new Scene(loader.load(), 900, 550);

            // Get current stage
            Stage stage = (Stage) greetingLabel.getScene().getWindow();
            stage.setScene(scene);
            stage.setTitle("UniConnect");
            stage.setMinWidth(900);
            stage.setMinHeight(550);
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML
    public void handleLike(javafx.event.ActionEvent event) {
        // Get the source button from the event
        Button sourceButton = (Button) event.getSource();

        // Determine which button was clicked and toggle its state
        if (sourceButton == likeButton1) {
            isLiked1 = !isLiked1;
            toggleLikeButton(sourceButton, isLiked1, "Alice Smith's post");
        } else if (sourceButton == likeButton2) {
            isLiked2 = !isLiked2;
            toggleLikeButton(sourceButton, isLiked2, "Bob Johnson's post");
        } else if (sourceButton == likeButton3) {
            isLiked3 = !isLiked3;
            toggleLikeButton(sourceButton, isLiked3, "Sarah Wilson's post");
        } else if (sourceButton == likeButton) {
            // Fallback for legacy like button
            isLiked = !isLiked;
            toggleLikeButton(sourceButton, isLiked, "Legacy post");
        }
    }

    private void toggleLikeButton(Button button, boolean liked, String postInfo) {
        if (liked) {
            // Change to liked state (black background)
            button.getStyleClass().remove("like-btn");
            button.getStyleClass().add("like-btn");
            button.getStyleClass().add("liked");
            button.setText("👍 Liked");
            System.out.println(postInfo + " liked!");
        } else {
            // Change back to normal state (blue background)
            button.getStyleClass().remove("liked");
            button.setText("👍 Like");
            System.out.println(postInfo + " unliked!");
        }
    }
}