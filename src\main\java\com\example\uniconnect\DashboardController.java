package com.example.uniconnect;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.image.ImageView;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.io.IOException;

public class DashboardController {
    @FXML
    private BorderPane dashboardContainer;

    @FXML
    private HBox header;

    @FXML
    private VBox sidebar;

    @FXML
    private VBox contentLeft;

    @FXML
    private VBox contentRight;

    @FXML
    private Label greetingLabel;

    @FXML
    private Label dateLabel;

    @FXML
    private ImageView profileImageView;

    @FXML
    private TextArea postTextArea;

    @FXML
    private ComboBox<String> categoryComboBox;

    @FXML
    private Button postButton;

    @FXML
    private Button logoutButton;

    @FXML
    private Button dashboardButton;

    @FXML
    private Button likeButton;

    private boolean isLiked = false;

    @FXML
    public void initialize() {
        // Set greeting to match reference design
        String userName = "Tommy"; // As shown in reference design
        if (Data.fullName != null && !Data.fullName.isEmpty()) {
            userName = Data.fullName;
        }

        // Set greeting to match reference design
        String greeting = "Good morning, " + userName;
        greetingLabel.setText(greeting);

        // Set date to match reference design format
        dateLabel.setText("Friday, 25 April, 2025");

        // Initialize category ComboBox
        categoryComboBox.getItems().addAll(
            "Academic", "Buy & Sell", "Lost & Found", "Events", "General"
        );
    }

    @FXML
    public void handlePost() {
        // This method would handle posting new content
        // For now, just print the post text and clear the input
        if (postTextArea.getText().isEmpty()) {
            return;
        }

        // Print the post text to console
        System.out.println("New post: " + postTextArea.getText());
        if (categoryComboBox.getValue() != null) {
            System.out.println("Category: " + categoryComboBox.getValue());
        }

        // Clear the input fields
        postTextArea.clear();
        categoryComboBox.setValue(null);
    }

    @FXML
    public void handleLogout() {
        try {
            // Clear user data
            Data.userId = 0;
            Data.studentId = null;
            Data.fullName = null;

            // Load login screen
            FXMLLoader loader = new FXMLLoader(getClass().getResource("LoginSignup.fxml"));
            Scene scene = new Scene(loader.load(), 900, 550);

            // Get current stage
            Stage stage = (Stage) greetingLabel.getScene().getWindow();
            stage.setScene(scene);
            stage.setTitle("UniConnect");
            stage.setMinWidth(900);
            stage.setMinHeight(550);
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML
    public void handleLike() {
        // Toggle like state
        isLiked = !isLiked;

        if (isLiked) {
            // Change to liked state (black background)
            likeButton.getStyleClass().remove("like-button");
            likeButton.getStyleClass().add("like-button");
            likeButton.getStyleClass().add("liked");
            likeButton.setText("👍 Liked");
            System.out.println("Post liked!");
        } else {
            // Change back to normal state (blue background)
            likeButton.getStyleClass().remove("liked");
            likeButton.setText("👍 Like");
            System.out.println("Post unliked!");
        }
    }
}