package com.example.uniconnect;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.image.ImageView;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import javafx.event.ActionEvent;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

public class DashboardController {
    @FXML
    private BorderPane dashboardContainer;

    @FXML
    private HBox header;

    @FXML
    private VBox sidebar;

    @FXML
    private VBox contentLeft;

    @FXML
    private VBox contentRight;

    @FXML
    private Label greetingLabel;

    @FXML
    private Label dateLabel;

    @FXML
    private Label userNameLabel;

    @FXML
    private ImageView profileImageView;

    @FXML
    private TextArea postTextArea;

    @FXML
    private ComboBox<String> categoryComboBox;

    @FXML
    private Button postButton;

    @FXML
    private Button logoutButton;

    @FXML
    private Button dashboardButton;

    @FXML
    private Button likeButton;

    @FXML
    private Button likeButton2;

    // Like state tracking for multiple buttons
    private Map<Button, Boolean> likeStates = new HashMap<>();

    @FXML
    public void initialize() {
        // Set user name and greeting
        String userName = "Tommy"; // Default name
        if (Data.fullName != null && !Data.fullName.isEmpty()) {
            userName = Data.fullName;
        }

        // Update greeting and user name labels
        String greeting = getGreeting() + ", " + userName.split(" ")[0];
        greetingLabel.setText(greeting);

        if (userNameLabel != null) {
            userNameLabel.setText(userName);
        }

        // Set current date with proper formatting
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, dd MMMM, yyyy");
        dateLabel.setText(now.format(formatter));

        // Initialize category ComboBox
        categoryComboBox.getItems().addAll(
            "Academic", "Buy & Sell", "Lost & Found", "Events", "General", "Study Group"
        );

        // Initialize like button states
        if (likeButton != null) {
            likeStates.put(likeButton, false);
        }
        if (likeButton2 != null) {
            likeStates.put(likeButton2, false);
        }
    }

    private String getGreeting() {
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();

        if (hour < 12) {
            return "Good morning";
        } else if (hour < 17) {
            return "Good afternoon";
        } else {
            return "Good evening";
        }
    }

    @FXML
    public void handlePost() {
        // This method would handle posting new content
        // For now, just print the post text and clear the input
        if (postTextArea.getText().isEmpty()) {
            return;
        }

        // Print the post text to console
        System.out.println("New post: " + postTextArea.getText());
        if (categoryComboBox.getValue() != null) {
            System.out.println("Category: " + categoryComboBox.getValue());
        }

        // Clear the input fields
        postTextArea.clear();
        categoryComboBox.setValue(null);
    }

    @FXML
    public void handleLogout() {
        try {
            // Clear user data
            Data.userId = 0;
            Data.studentId = null;
            Data.fullName = null;

            // Load login screen
            FXMLLoader loader = new FXMLLoader(getClass().getResource("LoginSignup.fxml"));
            Scene scene = new Scene(loader.load(), 900, 550);

            // Get current stage
            Stage stage = (Stage) greetingLabel.getScene().getWindow();
            stage.setScene(scene);
            stage.setTitle("UniConnect");
            stage.setMinWidth(900);
            stage.setMinHeight(550);
            stage.show();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @FXML
    public void handleLike(ActionEvent event) {
        Button clickedButton = (Button) event.getSource();
        handleLikeButton(clickedButton);
    }

    private void handleLikeButton(Button button) {
        // Get current like state for this button
        boolean currentState = likeStates.getOrDefault(button, false);
        boolean newState = !currentState;

        // Update the state
        likeStates.put(button, newState);

        if (newState) {
            // Change to liked state (black background)
            if (!button.getStyleClass().contains("liked")) {
                button.getStyleClass().add("liked");
            }
            button.setText("👍 Liked");
            System.out.println("Post liked!");

            // Add a subtle animation effect
            button.setScaleX(1.1);
            button.setScaleY(1.1);

            // Reset scale after a short delay
            javafx.animation.Timeline timeline = new javafx.animation.Timeline(
                new javafx.animation.KeyFrame(
                    javafx.util.Duration.millis(150),
                    e -> {
                        button.setScaleX(1.0);
                        button.setScaleY(1.0);
                    }
                )
            );
            timeline.play();

        } else {
            // Change back to normal state (blue background)
            button.getStyleClass().remove("liked");
            button.setText("👍 Like");
            System.out.println("Post unliked!");
        }
    }

    // Alternative method for direct button calls (backward compatibility)
    @FXML
    public void handleLike() {
        if (likeButton != null) {
            handleLikeButton(likeButton);
        }
    }
}