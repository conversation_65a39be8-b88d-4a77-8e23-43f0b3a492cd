<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a5fc6269-522e-4177-aba3-4f691541e5bc" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2w5fydnnKjJrynQXS3RXYKIJCVZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/OneDrive/Desktop/AOOP_Project/UniConnect/lib/mysql-connector-j-9.3.0.jar&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.JavaFX&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\OneDrive\Desktop\AOOP_Project\UniConnect\src\main\resources\com\example\uniconnect" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="a5fc6269-522e-4177-aba3-4f691541e5bc" name="Changes" comment="" />
      <created>1745336614201</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1745336614201</updated>
      <workItem from="1745336615262" duration="1763000" />
      <workItem from="1745339419293" duration="8092000" />
      <workItem from="1745433507578" duration="1705000" />
      <workItem from="1745436756475" duration="1014000" />
      <workItem from="1745437945115" duration="1758000" />
      <workItem from="1745578378239" duration="2488000" />
      <workItem from="1745759945594" duration="8026000" />
      <workItem from="1745772504950" duration="1545000" />
      <workItem from="1748089243119" duration="486000" />
      <workItem from="1748091843895" duration="589000" />
      <workItem from="1748092713210" duration="989000" />
      <workItem from="1748094266966" duration="1091000" />
      <workItem from="1748095386066" duration="634000" />
      <workItem from="1748155852212" duration="1176000" />
      <workItem from="1748161505687" duration="3032000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/UniConnect$UniConnectApplication.ic" NAME="UniConnectApplication Coverage Results" MODIFIED="1748093437120" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false">
      <FILTER>com.example.uniconnect.*</FILTER>
    </SUITE>
  </component>
</project>