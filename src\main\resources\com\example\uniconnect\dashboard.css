/* Global Styles - Enhanced DBMS project matching design */
.root {
    -fx-font-family: "Segoe UI", "Roboto", "Arial", sans-serif;
    -fx-font-size: 14px;
    -fx-background-color: #F5F7FA;
}

/* Base Card Styling - Reusable for consistency */
.base-card {
    -fx-background-color: #FFFFFF;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

/* Base Button Styling - Reusable for consistency */
.base-button {
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-font-weight: 500;
    -fx-transition: all 0.2s ease;
}

.base-button:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 8, 0, 0, 2);
    -fx-translate-y: -1px;
}

/* Sidebar Styles - Enhanced modern design */
.sidebar {
    -fx-background-color: #1A237E;
    -fx-pref-width: 200px;
    -fx-spacing: 0;
    -fx-padding: 25 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 8, 0, 2, 0);
}

.sidebar .active {
    -fx-background-color: #0D47A1;
}

.sidebar .sidebar-item {
    -fx-padding: 15 25;
    -fx-text-fill: #FFFFFF;
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-cursor: hand;
    -fx-spacing: 15;
    -fx-alignment: center-left;
    -fx-transition: all 0.2s ease;
}

.sidebar .sidebar-item:hover {
    -fx-background-color: rgba(255, 255, 255, 0.1);
    -fx-effect: dropshadow(gaussian, rgba(255,255,255,0.1), 2, 0, 0, 1);
}

/* Header Styles - Modern clean design */
.header {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20 25;
    -fx-spacing: 15;
    -fx-border-color: #E8EAED;
    -fx-border-width: 0 0 1 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.greeting {
    -fx-font-weight: 500;
    -fx-font-size: 17px;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.notification {
    -fx-spacing: 18;
    -fx-alignment: center-right;
}

.nav-icon, .icon-button {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 18px;
    -fx-cursor: hand;
    -fx-padding: 10;
    -fx-background-radius: 8px;
    -fx-min-width: 40px;
    -fx-min-height: 40px;
}

.nav-icon:hover, .icon-button:hover {
    -fx-background-color: #F1F3F4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

.profile-img {
    -fx-background-radius: 50%;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 4, 0, 0, 2);
}

/* Main Content Styles - Enhanced modern layout */
.main-content {
    -fx-background-color: transparent;
    -fx-spacing: 25;
    -fx-padding: 25;
}

/* Post Input Area Styles - Modern card design */
.post-input-container {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.post-input-area {
    -fx-alignment: center-left;
    -fx-spacing: 15;
}

.post-input {
    -fx-background-color: #F8F9FA;
    -fx-padding: 15;
    -fx-background-radius: 8px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
}

.post-textarea {
    -fx-pref-height: 45;
    -fx-background-color: #F8F9FA;
    -fx-border-color: #E8EAED;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 12;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #5F6368;
}

.post-textarea:focused {
    -fx-border-color: #1877F2;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.2), 4, 0, 0, 1);
}

.post-actions {
    -fx-alignment: center-left;
    -fx-spacing: 15;
}

.post-button {
    -fx-background-color: #1A237E;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 10 20;
    -fx-background-radius: 8px;
    -fx-font-weight: 500;
    -fx-cursor: hand;
    -fx-min-width: 100px;
}

.post-button:hover {
    -fx-background-color: #283593;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 8, 0, 0, 2);
    -fx-translate-y: -1px;
}

.photo-button {
    -fx-background-color: #F8F9FA;
    -fx-text-fill: #5F6368;
    -fx-padding: 10 15;
    -fx-background-radius: 8px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-font-weight: 500;
}

.photo-button:hover {
    -fx-background-color: #F1F3F4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 1);
}

.category-dropdown {
    -fx-background-color: #F8F9FA;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8 12;
}

.profile-circle {
    -fx-fill: #E8EAED;
    -fx-stroke: #DADCE0;
    -fx-stroke-width: 2;
}

/* Post Styles - Enhanced card design */
.post {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-spacing: 15;
}

.post:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 8, 0, 0, 4);
    -fx-translate-y: -1px;
}

.username {
    -fx-font-weight: 600;
    -fx-font-size: 15px;
    -fx-text-fill: #202124;
}

.timestamp {
    -fx-text-fill: #5F6368;
    -fx-font-size: 13px;
}

.post-text {
    -fx-font-size: 14px;
    -fx-text-fill: #202124;
    -fx-line-spacing: 2px;
}

.post-buttons {
    -fx-spacing: 12;
    -fx-alignment: center-left;
}

/* Enhanced Like Button with State Management */
.like-button {
    -fx-background-color: #1877F2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 8 16;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-font-weight: 500;
    -fx-font-size: 13px;
}

.like-button:hover {
    -fx-background-color: #166FE5;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.3), 6, 0, 0, 2);
}

.like-button.liked {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
}

.like-button.liked:hover {
    -fx-background-color: #333333;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 2);
}

.comment-button {
    -fx-background-color: #F8F9FA;
    -fx-text-fill: #5F6368;
    -fx-padding: 8 16;
    -fx-background-radius: 8px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-font-weight: 500;
    -fx-font-size: 13px;
}

.comment-button:hover {
    -fx-background-color: #F1F3F4;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 4, 0, 0, 1);
}

/* Category Tags - Enhanced styling */
.category-tag {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.5px;
}

/* Class Routine Styles - Enhanced modern design */
.routine-section {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-max-height: 600;
    -fx-spacing: 20;
}

.routine-title {
    -fx-font-size: 19px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.class-container {
    -fx-background-color: transparent;
    -fx-max-height: 500;
    -fx-spacing: 15;
}

.class-item {
    -fx-background-color: #F8F9FA;
    -fx-padding: 18;
    -fx-background-radius: 10px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 10px;
    -fx-spacing: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.class-item:hover {
    -fx-background-color: #FFFFFF;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 6, 0, 0, 3);
    -fx-translate-y: -1px;
}

.class-details {
    -fx-spacing: 10;
}

.subject-label {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
}

.class-time {
    -fx-font-size: 14px;
    -fx-font-weight: 500;
    -fx-text-fill: #5F6368;
    -fx-spacing: 8;
    -fx-alignment: center-left;
}

.class-location {
    -fx-spacing: 8;
    -fx-alignment: center-left;
}

/* Enhanced Time and Room Tags */
.time-tag {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

.room-tag {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

.section-tag {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

/* Legacy support for existing styles */
.routine {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.section-header {
    -fx-font-size: 19px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.class-entry {
    -fx-padding: 15;
    -fx-spacing: 10;
}

.subject {
    -fx-font-weight: 600;
    -fx-font-size: 16px;
    -fx-text-fill: #202124;
    -fx-min-width: 120;
}

.time {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-min-width: 100;
}

.section {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-min-width: 80;
}

.room {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-min-width: 80;
}





/* Enhanced Content Layout Styles */
.content-header {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-spacing: 20;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.welcome-section {
    -fx-spacing: 8;
}

.welcome-text {
    -fx-font-size: 16px;
    -fx-text-fill: #5F6368;
    -fx-font-weight: 400;
}

.user-name {
    -fx-font-size: 24px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.stats-section {
    -fx-spacing: 20;
    -fx-alignment: center-right;
}

.stat-card {
    -fx-padding: 20;
    -fx-background-color: #F8F9FA;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-alignment: center;
    -fx-min-width: 140;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.stat-card:hover {
    -fx-background-color: #FFFFFF;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 6, 0, 0, 3);
    -fx-translate-y: -1px;
}

.stat-label {
    -fx-font-size: 13px;
    -fx-text-fill: #5F6368;
    -fx-font-weight: 500;
}

.stat-value {
    -fx-font-size: 22px;
    -fx-font-weight: 700;
    -fx-text-fill: #1A237E;
    -fx-letter-spacing: 0.3px;
}

.content-body {
    -fx-spacing: 25;
}

.feed-section {
    -fx-spacing: 20;
    -fx-background-color: #FFFFFF;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-padding: 25;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.feed-content {
    -fx-spacing: 15;
}

.schedule-section {
    -fx-background-color: #FFFFFF;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-padding: 25;
    -fx-spacing: 20;
    -fx-min-width: 320;
    -fx-max-height: 600;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.schedule-content {
    -fx-spacing: 15;
}

/* Class routine styles - matching DBMS exactly */
.class-list {
    -fx-background-color: transparent;
    -fx-max-height: 400;
}

.class-item {
    -fx-background-color: white;
    -fx-border-width: 0 0 0 5;
    -fx-border-color: #000;
    -fx-background-radius: 5;
    -fx-padding: 15;
    -fx-spacing: 10;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
    -fx-margin: 0 0 15 0;
}

.class-item:hover {
    -fx-translate-y: -3;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.15), 12, 0, 0, 4);
}

.subject-math,
.subject-physics,
.subject-chemistry,
.subject-english {
    -fx-border-color: #000;
}

.class-details {
    -fx-spacing: 10;
}

.subject-label {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #000;
}

.class-time {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #000;
    -fx-spacing: 5;
    -fx-alignment: center-left;
}

.class-location {
    -fx-spacing: 8;
}

.section-tag {
    -fx-background-color: #e8f5e8;
    -fx-text-fill: #2e7d32;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}

.room-tag {
    -fx-background-color: #fff3e0;
    -fx-text-fill: #f57c00;
    -fx-padding: 4 8;
    -fx-background-radius: 12;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
}
