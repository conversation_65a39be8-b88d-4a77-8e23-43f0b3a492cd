/* UniConnect Dashboard - Refined Three-Color Theme (<PERSON>, <PERSON>, <PERSON> Cyan) */
.root {
    -fx-font-family: "Arial";
    -fx-font-size: 14px;
    -fx-background-color: #E0F7FA;
}

/* Header Styles - White Background with Black Text */
.header {
    -fx-background-color: #FFFFFF;
    -fx-padding: 15 20;
    -fx-spacing: 15;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 0 0 1 0;
}

.greeting {
    -fx-font-weight: 400;
    -fx-font-size: 16px;
    -fx-text-fill: #333333;
}

.notification {
    -fx-spacing: 15;
    -fx-alignment: center-right;
}

.nav-icon {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-font-size: 16px;
    -fx-cursor: hand;
    -fx-padding: 8;
    -fx-background-radius: 4px;
    -fx-min-width: 32px;
    -fx-min-height: 32px;
}

.nav-icon:hover {
    -fx-background-color: #F5F5F5;
}

.profile-circle-header {
    -fx-fill: #CCCCCC;
    -fx-stroke: #999999;
    -fx-stroke-width: 1;
}

/* Profile Circle Styles */
.profile-circle-input {
    -fx-fill: #E0E0E0;
    -fx-stroke: #CCCCCC;
    -fx-stroke-width: 2;
}

.profile-circle-post {
    -fx-fill: #F0F0F0;
    -fx-stroke: #DDDDDD;
    -fx-stroke-width: 2;
}

/* Sidebar Styles - Black Background with White Text */
.sidebar {
    -fx-background-color: #000000;
    -fx-pref-width: 205px;
    -fx-spacing: 0;
    -fx-padding: 0;
}

.logo-section {
    -fx-padding: 20;
    -fx-alignment: center;
}

.logo-text {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #FFFFFF;
}

.nav-menu, .bottom-menu {
    -fx-spacing: 0;
}

.sidebar-item {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 12 20;
    -fx-alignment: center-left;
    -fx-cursor: hand;
    -fx-min-width: 205px;
    -fx-max-width: 205px;
}

.sidebar-item:hover {
    -fx-background-color: #333333;
}

.sidebar-item.active {
    -fx-background-color: #444444;
}

.sidebar-icon {
    -fx-font-size: 16px;
    -fx-text-fill: #FFFFFF;
}

.sidebar-text {
    -fx-font-size: 14px;
    -fx-text-fill: #FFFFFF;
    -fx-font-weight: 400;
}

/* Main Content Area - Matching DBMS Layout */
.main-content {
    -fx-background-color: transparent;
    -fx-spacing: 20;
    -fx-padding: 20;
}

/* Post Input Card - DBMS Style */
.post-input-card {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 8px;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.post-input {
    -fx-background-color: #F8F9FA;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 12;
    -fx-font-size: 14px;
    -fx-prompt-text-fill: #999999;
}

.post-input:focused {
    -fx-border-color: #4285F4;
    -fx-border-width: 2px;
}

.post-actions {
    -fx-spacing: 10;
    -fx-alignment: center-left;
}

.category-select {
    -fx-background-color: #F8F9FA;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 8 12;
    -fx-font-size: 14px;
    -fx-min-width: 175px;
    -fx-pref-width: 175px;
}

.photo-btn {
    -fx-background-color: #F8F9FA;
    -fx-text-fill: #666666;
    -fx-padding: 8 16;
    -fx-background-radius: 8px;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-cursor: hand;
    -fx-font-size: 14px;
    -fx-min-height: 45px;
    -fx-pref-height: 45px;
}

.photo-btn:hover {
    -fx-background-color: #F0F0F0;
}

.post-btn {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 8 20;
    -fx-background-radius: 6px;
    -fx-cursor: hand;
    -fx-font-size: 14px;
    -fx-font-weight: 500;
}

.post-btn:hover {
    -fx-background-color: #333333;
}

/* Section Title - DBMS Style */
.section-title {
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #333333;
    -fx-padding: 0 0 15 0;
}

/* Posts Container - DBMS Style */
.posts-container {
    -fx-spacing: 0;
}

.post-card {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 0 0 1 0;
    -fx-spacing: 12;
}

.post-card:hover {
    -fx-background-color: #FAFAFA;
}

/* Individual Post Card Styles with Rounded Corners */
.post-card-individual {
    -fx-background-color: #FFFFFF;
    -fx-padding: 20;
    -fx-background-radius: 12px;
    -fx-border-color: #E0E0E0;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-spacing: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.post-card-individual:hover {
    -fx-background-color: #FAFAFA;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 8, 0, 0, 4);
}

/* ScrollPane Styles for Posts */
.posts-scroll-pane {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
}

.posts-scroll-pane .viewport {
    -fx-background-color: transparent;
}

.posts-scroll-pane .scroll-bar:vertical {
    -fx-background-color: #F5F5F5;
    -fx-border-color: transparent;
    -fx-background-radius: 5px;
    -fx-border-radius: 5px;
    -fx-pref-width: 8px;
}

.posts-scroll-pane .scroll-bar:vertical .track {
    -fx-background-color: #F0F0F0;
    -fx-background-radius: 5px;
    -fx-border-color: transparent;
}

.posts-scroll-pane .scroll-bar:vertical .thumb {
    -fx-background-color: #CCCCCC;
    -fx-background-radius: 5px;
    -fx-border-color: transparent;
}

.posts-scroll-pane .scroll-bar:vertical .thumb:hover {
    -fx-background-color: #AAAAAA;
}

.posts-container-scrollable {
    -fx-padding: 5;
}

/* Post Image Styles */
.post-image-container {
    -fx-padding: 10 0;
    -fx-alignment: center;
}

.post-image {
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.post-author {
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-text-fill: #333333;
}

.post-category {
    -fx-background-color: #E3F2FD;
    -fx-text-fill: #1976D2;
    -fx-padding: 4 8;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

.post-category.lost-found {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
}

/* Black Category Tag for Top Right Position */
.post-category-black {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 4 8;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 500;
}

.post-time {
    -fx-font-size: 12px;
    -fx-text-fill: #999999;
}

.post-content {
    -fx-font-size: 14px;
    -fx-text-fill: #333333;
    -fx-line-spacing: 2px;
}

.post-actions-bar {
    -fx-spacing: 15;
    -fx-alignment: center-left;
}

.action-btn {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-padding: 6 12;
    -fx-cursor: hand;
    -fx-font-size: 13px;
    -fx-background-radius: 4px;
}

.like-btn {
    -fx-text-fill: #666666;
}

.like-btn:hover {
    -fx-background-color: #F0F0F0;
}

.comment-btn {
    -fx-text-fill: #666666;
}

.comment-btn:hover {
    -fx-background-color: #F0F0F0;
}

/* Legacy Like Button Support */
.like-button {
    -fx-background-color: #1877F2;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 8 16;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-font-weight: 500;
    -fx-font-size: 13px;
}

.like-button:hover {
    -fx-background-color: #166FE5;
    -fx-effect: dropshadow(gaussian, rgba(24,119,242,0.3), 6, 0, 0, 2);
}

.like-button.liked {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
}

.like-button.liked:hover {
    -fx-background-color: #333333;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 2);
}

/* Class Routine Styles - Enhanced modern design */
.routine-section {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
    -fx-max-height: 600;
    -fx-spacing: 20;
}

.routine-title {
    -fx-font-size: 19px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.class-container {
    -fx-background-color: transparent;
    -fx-max-height: 500;
    -fx-spacing: 15;
}

.class-item {
    -fx-background-color: #F8F9FA;
    -fx-padding: 18;
    -fx-background-radius: 10px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 10px;
    -fx-spacing: 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.class-item:hover {
    -fx-background-color: #FFFFFF;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 6, 0, 0, 3);
    -fx-translate-y: -1px;
}

.class-details {
    -fx-spacing: 10;
}

.subject-label {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
}

.class-time {
    -fx-font-size: 13px;
    -fx-font-weight: 400;
    -fx-text-fill: #666666;
}

.class-location {
    -fx-spacing: 8;
    -fx-alignment: center-left;
}

/* Enhanced Time and Room Tags */
.time-tag {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

.room-tag {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

.section-tag {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-letter-spacing: 0.3px;
}

/* Legacy support for existing styles */
.routine {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.section-header {
    -fx-font-size: 19px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.class-entry {
    -fx-padding: 15;
    -fx-spacing: 10;
}

.subject {
    -fx-font-weight: 600;
    -fx-font-size: 16px;
    -fx-text-fill: #202124;
    -fx-min-width: 120;
}

.time {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-min-width: 100;
}

.section {
    -fx-background-color: #000000;
    -fx-text-fill: #FFFFFF;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-min-width: 80;
}

.room {
    -fx-background-color: #FFF3E0;
    -fx-text-fill: #F57C00;
    -fx-padding: 6 12;
    -fx-background-radius: 12px;
    -fx-font-size: 12px;
    -fx-font-weight: 600;
    -fx-min-width: 80;
}





/* Enhanced Content Layout Styles */
.content-header {
    -fx-background-color: #FFFFFF;
    -fx-padding: 25;
    -fx-spacing: 20;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.welcome-section {
    -fx-spacing: 8;
}

.welcome-text {
    -fx-font-size: 16px;
    -fx-text-fill: #5F6368;
    -fx-font-weight: 400;
}

.user-name {
    -fx-font-size: 24px;
    -fx-font-weight: 600;
    -fx-text-fill: #202124;
    -fx-letter-spacing: 0.2px;
}

.stats-section {
    -fx-spacing: 20;
    -fx-alignment: center-right;
}

.stat-card {
    -fx-padding: 20;
    -fx-background-color: #F8F9FA;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-alignment: center;
    -fx-min-width: 140;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 3, 0, 0, 1);
}

.stat-card:hover {
    -fx-background-color: #FFFFFF;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.12), 6, 0, 0, 3);
    -fx-translate-y: -1px;
}

.stat-label {
    -fx-font-size: 13px;
    -fx-text-fill: #5F6368;
    -fx-font-weight: 500;
}

.stat-value {
    -fx-font-size: 22px;
    -fx-font-weight: 700;
    -fx-text-fill: #1A237E;
    -fx-letter-spacing: 0.3px;
}

.content-body {
    -fx-spacing: 25;
}

.feed-section {
    -fx-spacing: 20;
    -fx-background-color: #FFFFFF;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-padding: 25;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

.feed-content {
    -fx-spacing: 15;
}

.schedule-section {
    -fx-background-color: #FFFFFF;
    -fx-background-radius: 12px;
    -fx-border-color: #E8EAED;
    -fx-border-width: 1px;
    -fx-border-radius: 12px;
    -fx-padding: 25;
    -fx-spacing: 20;
    -fx-min-width: 320;
    -fx-max-height: 600;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 3);
}

/* Routine Container Styles */
.routine-container {
    -fx-background-color: #E0F7FA;
    -fx-padding: 10;
    -fx-background-radius: 5;
}

/* Date Display Styles */
.date-label {
    -fx-text-fill: #000000;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-alignment: center;
    -fx-background-color: #FFFFFF;
    -fx-padding: 10;
    -fx-background-radius: 5;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

/* Calendar Status Styles */
.status-title {
    -fx-text-fill: #757575;
    -fx-font-size: 12px;
}

.status-button {
    -fx-background-color: #BBDEFB;
    -fx-text-fill: #2196F3;
    -fx-background-radius: 15;
    -fx-padding: 5 10;
    -fx-border-color: transparent;
    -fx-border-radius: 15;
}

/* Class Routine Styles */
.routine-title {
    -fx-text-fill: #000000;
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-padding: 5;
}

.routine-box {
    -fx-background-color: #FFFFFF;
    -fx-background-radius: 5;
    -fx-padding: 10;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.class-entry {
    -fx-spacing: 5;
    -fx-padding: 5 0;
}

.subject-label {
    -fx-text-fill: #000000;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
}

.time-label {
    -fx-text-fill: #388E3C;
    -fx-background-color: #C8E6C9;
    -fx-background-radius: 3;
    -fx-padding: 2 5;
}

.section-button {
    -fx-text-fill: #D32F2F;
    -fx-background-color: #FFCDD2;
    -fx-background-radius: 3;
    -fx-padding: 2 5;
    -fx-border-color: transparent;
}

.room-button {
    -fx-text-fill: #757575;
    -fx-background-color: #E0E0E0;
    -fx-background-radius: 3;
    -fx-padding: 2 5;
    -fx-border-color: transparent;
}
