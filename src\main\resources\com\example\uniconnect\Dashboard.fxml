<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.shape.Circle?>
<?import javafx.geometry.Insets?>

<BorderPane fx:id="dashboardContainer" stylesheets="@dashboard.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1"
          fx:controller="com.example.uniconnect.DashboardController" styleClass="root" prefWidth="1200" prefHeight="800">

    <!-- Top Header -->
    <top>
        <HBox styleClass="header" spacing="10">
            <Label fx:id="greetingLabel" text="Good morning, maine uddin shanto" styleClass="greeting"/>
            <Region HBox.hgrow="ALWAYS"/>
            <HBox styleClass="notification" spacing="10">
                <Button styleClass="icon-button" text="📧"/>
                <Button styleClass="icon-button" text="🔔"/>
                <ImageView fx:id="profileImageView" fitHeight="40" fitWidth="40" styleClass="profile-img">
                    <Image url="@images/uniconnect.png"/>
                </ImageView>
            </HBox>
        </HBox>
    </top>

    <!-- Left Sidebar -->
    <left>
        <VBox styleClass="sidebar" prefWidth="200">
            <!-- Logo -->
            <VBox styleClass="logo-section">
                <Label text="Uniconnect" styleClass="logo-text"/>
            </VBox>

            <!-- Menu Items -->
            <VBox styleClass="menu-items" spacing="5">
                <HBox styleClass="sidebar-item">
                    <Label text="📷" styleClass="sidebar-icon"/>
                    <Label text="Profile" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item active">
                    <Label text="🏠" styleClass="sidebar-icon"/>
                    <Label text="Home" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item">
                    <Label text="👥" styleClass="sidebar-icon"/>
                    <Label text="Study Group" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item">
                    <Label text="📝" styleClass="sidebar-icon"/>
                    <Label text="Notes" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item">
                    <Label text="🔧" styleClass="sidebar-icon"/>
                    <Label text="Academic Tools" styleClass="sidebar-text"/>
                </HBox>
            </VBox>

            <Region VBox.vgrow="ALWAYS"/>

            <!-- Settings Menu -->
            <VBox styleClass="menu-settings" spacing="5">
                <HBox styleClass="sidebar-item">
                    <Label text="⚙️" styleClass="sidebar-icon"/>
                    <Label text="Settings" styleClass="sidebar-text"/>
                </HBox>
                <HBox styleClass="sidebar-item" onMouseClicked="#handleLogout">
                    <Label text="🚪" styleClass="sidebar-icon"/>
                    <Label text="Logout" styleClass="sidebar-text"/>
                </HBox>
            </VBox>
        </VBox>
    </left>

    <!-- Center Content -->
    <center>
        <HBox spacing="0">
            <padding>
                <Insets top="20" right="0" bottom="20" left="20"/>
            </padding>

            <!-- Main Content Area -->
            <VBox styleClass="main-content" spacing="25" prefWidth="500" HBox.hgrow="ALWAYS">
                <!-- Welcome Header -->
                <VBox styleClass="content-header base-card" spacing="20">
                    <HBox spacing="20">
                        <VBox styleClass="welcome-section" HBox.hgrow="ALWAYS">
                            <Label text="Welcome back!" styleClass="welcome-text"/>
                            <Label fx:id="userNameLabel" text="Tommy" styleClass="user-name"/>
                        </VBox>
                        <HBox styleClass="stats-section" spacing="20">
                            <VBox styleClass="stat-card" alignment="CENTER">
                                <Label text="Posts" styleClass="stat-label"/>
                                <Label text="12" styleClass="stat-value"/>
                            </VBox>
                            <VBox styleClass="stat-card" alignment="CENTER">
                                <Label text="Likes" styleClass="stat-label"/>
                                <Label text="48" styleClass="stat-value"/>
                            </VBox>
                        </HBox>
                    </HBox>
                </VBox>

                <!-- Post Input Area -->
                <VBox styleClass="post-input-container base-card" spacing="20">
                    <HBox styleClass="post-input-area" spacing="15">
                        <Circle radius="22" styleClass="profile-circle"/>
                        <TextArea fx:id="postTextArea" promptText="What's on your mind?" styleClass="post-textarea" prefHeight="45" HBox.hgrow="ALWAYS"/>
                    </HBox>
                    <HBox styleClass="post-actions" spacing="15">
                        <ComboBox fx:id="categoryComboBox" promptText="Select Category" styleClass="category-dropdown" prefWidth="160"/>
                        <Button styleClass="photo-button" text="📷 Photo" prefWidth="120"/>
                        <Region HBox.hgrow="ALWAYS"/>
                        <Button fx:id="postButton" styleClass="post-button base-button" text="Post" onAction="#handlePost"/>
                    </HBox>
                </VBox>

                <!-- Recent Posts Section -->
                <VBox styleClass="feed-section base-card" spacing="20">
                    <Label text="Recent Posts" styleClass="section-header"/>

                    <VBox styleClass="feed-content" spacing="15">
                        <!-- Grace's Post -->
                        <VBox styleClass="post" spacing="15">
                            <HBox spacing="12">
                                <Circle radius="22" styleClass="profile-circle"/>
                                <VBox spacing="6" HBox.hgrow="ALWAYS">
                                    <HBox spacing="12">
                                        <Label text="Grace" styleClass="username"/>
                                        <Region HBox.hgrow="ALWAYS"/>
                                        <Button text="Academic" styleClass="category-tag"/>
                                    </HBox>
                                    <Label text="2 hours ago" styleClass="timestamp"/>
                                </VBox>
                            </HBox>
                            <Label text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" styleClass="post-text"/>
                            <HBox spacing="12" styleClass="post-buttons">
                                <Button fx:id="likeButton" text="👍 Like" styleClass="like-button" onAction="#handleLike"/>
                                <Button text="💬 Comment" styleClass="comment-button"/>
                            </HBox>
                        </VBox>

                        <!-- Additional Sample Post -->
                        <VBox styleClass="post" spacing="15">
                            <HBox spacing="12">
                                <Circle radius="22" styleClass="profile-circle"/>
                                <VBox spacing="6" HBox.hgrow="ALWAYS">
                                    <HBox spacing="12">
                                        <Label text="Alex" styleClass="username"/>
                                        <Region HBox.hgrow="ALWAYS"/>
                                        <Button text="Study Group" styleClass="category-tag"/>
                                    </HBox>
                                    <Label text="4 hours ago" styleClass="timestamp"/>
                                </VBox>
                            </HBox>
                            <Label text="Starting a study group for Advanced Algorithms. Meeting every Tuesday at 7 PM in the library. DM me if interested!" wrapText="true" styleClass="post-text"/>
                            <HBox spacing="12" styleClass="post-buttons">
                                <Button fx:id="likeButton2" text="👍 Like" styleClass="like-button" onAction="#handleLike"/>
                                <Button text="💬 Comment" styleClass="comment-button"/>
                            </HBox>
                        </VBox>
                    </VBox>
                </VBox>
            </VBox>

            <!-- Right Side: Calendar and Class Routine -->
            <VBox styleClass="right-sidebar" spacing="25" prefWidth="320">
                <padding>
                    <Insets top="0" right="20" bottom="0" left="20"/>
                </padding>

                <!-- Date and Calendar Status -->
                <VBox styleClass="content-header base-card" spacing="15">
                    <Label fx:id="dateLabel" text="Friday, 25 April, 2025" styleClass="section-header"/>
                    <VBox spacing="8">
                        <Label text="Calendar Status" styleClass="welcome-text"/>
                        <Label text="📅 In progress" styleClass="stat-value"/>
                    </VBox>
                </VBox>

                <!-- Class Routine Section -->
                <VBox styleClass="routine-section base-card" spacing="20">
                    <Label text="Your Class Routine" styleClass="routine-title"/>
                    <ScrollPane styleClass="class-container" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED">
                        <VBox spacing="15">
                            <!-- Mathematics Class -->
                            <VBox styleClass="class-item">
                                <VBox styleClass="class-details" spacing="10">
                                    <Label text="Mathematics" styleClass="subject-label"/>
                                    <HBox spacing="8" styleClass="class-time">
                                        <Label text="🕒" styleClass="time-icon"/>
                                        <Label text="9:00 AM - 10:30 AM" styleClass="class-time"/>
                                    </HBox>
                                    <HBox spacing="10" styleClass="class-location">
                                        <Label text="Section A" styleClass="section-tag"/>
                                        <Label text="Room 101" styleClass="room-tag"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Physics Class -->
                            <VBox styleClass="class-item">
                                <VBox styleClass="class-details" spacing="10">
                                    <Label text="Physics" styleClass="subject-label"/>
                                    <HBox spacing="8" styleClass="class-time">
                                        <Label text="🕒" styleClass="time-icon"/>
                                        <Label text="11:00 AM - 12:30 PM" styleClass="class-time"/>
                                    </HBox>
                                    <HBox spacing="10" styleClass="class-location">
                                        <Label text="Section B" styleClass="section-tag"/>
                                        <Label text="Room 203" styleClass="room-tag"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Chemistry Class -->
                            <VBox styleClass="class-item">
                                <VBox styleClass="class-details" spacing="10">
                                    <Label text="Chemistry" styleClass="subject-label"/>
                                    <HBox spacing="8" styleClass="class-time">
                                        <Label text="🕒" styleClass="time-icon"/>
                                        <Label text="2:00 PM - 3:30 PM" styleClass="class-time"/>
                                    </HBox>
                                    <HBox spacing="10" styleClass="class-location">
                                        <Label text="Section A" styleClass="section-tag"/>
                                        <Label text="Room 305" styleClass="room-tag"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- English Class -->
                            <VBox styleClass="class-item">
                                <VBox styleClass="class-details" spacing="10">
                                    <Label text="English" styleClass="subject-label"/>
                                    <HBox spacing="8" styleClass="class-time">
                                        <Label text="🕒" styleClass="time-icon"/>
                                        <Label text="4:00 PM - 5:30 PM" styleClass="class-time"/>
                                    </HBox>
                                    <HBox spacing="10" styleClass="class-location">
                                        <Label text="Section C" styleClass="section-tag"/>
                                        <Label text="Room 112" styleClass="room-tag"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Computer Science Class -->
                            <VBox styleClass="class-item">
                                <VBox styleClass="class-details" spacing="10">
                                    <Label text="Computer Science" styleClass="subject-label"/>
                                    <HBox spacing="8" styleClass="class-time">
                                        <Label text="🕒" styleClass="time-icon"/>
                                        <Label text="6:00 PM - 7:30 PM" styleClass="class-time"/>
                                    </HBox>
                                    <HBox spacing="10" styleClass="class-location">
                                        <Label text="Section A" styleClass="section-tag"/>
                                        <Label text="Room 401" styleClass="room-tag"/>
                                    </HBox>
                                </VBox>
                            </VBox>

                            <!-- Advanced Algorithms Class -->
                            <VBox styleClass="class-item">
                                <VBox styleClass="class-details" spacing="10">
                                    <Label text="Advanced Algorithms" styleClass="subject-label"/>
                                    <HBox spacing="8" styleClass="class-time">
                                        <Label text="🕒" styleClass="time-icon"/>
                                        <Label text="8:00 PM - 9:30 PM" styleClass="class-time"/>
                                    </HBox>
                                    <HBox spacing="10" styleClass="class-location">
                                        <Label text="Section A" styleClass="section-tag"/>
                                        <Label text="Room 501" styleClass="room-tag"/>
                                    </HBox>
                                </VBox>
                            </VBox>
                        </VBox>
                    </ScrollPane>
                </VBox>
            </VBox>
        </HBox>
    </center>
</BorderPane>