<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.shape.Circle?>

<BorderPane fx:id="dashboardContainer" prefHeight="800" prefWidth="1200" styleClass="root" stylesheets="@dashboard.css" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.example.uniconnect.DashboardController">

    <!-- Top Header - Matching DBMS Design -->
    <top>
        <HBox spacing="15" styleClass="header">
            <Label fx:id="greetingLabel" styleClass="greeting" text="Good morning, <PERSON>" />
            <Region HBox.hgrow="ALWAYS" />
            <HBox spacing="15" styleClass="notification">
                <Button styleClass="nav-icon" text="💬" />
                <Button styleClass="nav-icon" text="🔔" />
                <Circle radius="18" styleClass="profile-circle-header" />
            </HBox>
        </HBox>
    </top>

    <!-- Left Sidebar - Matching DBMS Design -->
    <left>
        <VBox prefWidth="205" styleClass="sidebar">
            <!-- Logo Section -->
            <VBox styleClass="logo-section">
                <Label styleClass="logo-text" text="Uniconnect" />
            </VBox>

            <!-- Navigation Menu -->
            <VBox spacing="0" styleClass="nav-menu">
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label styleClass="sidebar-icon" text="👤" />
                            <Label styleClass="sidebar-text" text="Profile" />
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item active">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label styleClass="sidebar-icon" text="🏠" />
                            <Label styleClass="sidebar-text" text="Home" />
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label styleClass="sidebar-icon" text="👥" />
                            <Label styleClass="sidebar-text" text="Study Group" />
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label styleClass="sidebar-icon" text="📝" />
                            <Label styleClass="sidebar-text" text="Notes" />
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label styleClass="sidebar-icon" text="🔧" />
                            <Label styleClass="sidebar-text" text="Academic Tools" />
                        </HBox>
                    </graphic>
                </Button>
            </VBox>

            <Region VBox.vgrow="ALWAYS" />

            <!-- Bottom Menu -->
            <VBox spacing="0" styleClass="bottom-menu">
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Label styleClass="sidebar-icon" text="⚙️" />
                            <Label styleClass="sidebar-text" text="Settings" />
                        </HBox>
                    </graphic>
                </Button>
            </VBox>
        </VBox>
    </left>

    <!-- Center Content - Matching DBMS Layout -->
    <center>
        <HBox spacing="20">
            <padding>
                <Insets bottom="20" left="20" right="20" top="20" />
            </padding>

            <!-- Main Feed Area -->
            <VBox prefWidth="500" spacing="20" styleClass="main-content" HBox.hgrow="ALWAYS">
                <!-- Post Input Card -->
                <VBox spacing="15" styleClass="post-input-card">
                    <HBox alignment="CENTER_LEFT" spacing="12">
                        <Circle radius="20" styleClass="profile-circle-input" />
                        <TextArea fx:id="postTextArea" prefHeight="50" promptText="What's on your mind?" styleClass="post-input" HBox.hgrow="ALWAYS" />
                    </HBox>
                    <HBox spacing="10" styleClass="post-actions">
                        <ComboBox fx:id="categoryComboBox" editable="true" prefHeight="44.0" prefWidth="175.0" promptText="Select Category" styleClass="category-select" />
                        <Button prefHeight="45.0" prefWidth="89.0" styleClass="photo-btn" text="📷 Photo" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Button fx:id="postButton" onAction="#handlePost" styleClass="post-btn" text="Post" />
                    </HBox>
                </VBox>

                <!-- Posts Feed -->
                <VBox spacing="15">
                    <Label styleClass="section-title" text="Recent Posts" />

                    <!-- Alice Smith Post -->
                    <VBox spacing="12" styleClass="post-card-individual">
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Circle radius="18" styleClass="profile-circle-post" />
                            <VBox spacing="2" HBox.hgrow="ALWAYS">
                                <HBox alignment="CENTER_LEFT" spacing="8">
                                    <Label styleClass="post-author" text="Alice Smith" />
                                    <Label alignment="TOP_CENTER" styleClass="post-category" text="Academic" />
                                </HBox>
                                <Label styleClass="post-time" text="2 hours ago" />
                            </VBox>
                        </HBox>
                        <Label styleClass="post-content" text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" />
                        <HBox spacing="15" styleClass="post-actions-bar">
                            <Button fx:id="likeButton1" onAction="#handleLike" styleClass="action-btn like-btn" text="👍 Like" />
                            <Button styleClass="action-btn comment-btn" text="💬 Comment" />
                        </HBox>
                    </VBox>

                    <!-- Bob Johnson Post -->
                    <VBox spacing="12" styleClass="post-card-individual">
                        <HBox alignment="CENTER_LEFT" spacing="12">
                            <Circle radius="18" styleClass="profile-circle-post" />
                            <VBox spacing="2" HBox.hgrow="ALWAYS">
                                <HBox alignment="CENTER_LEFT" spacing="8">
                                    <Label styleClass="post-author" text="Bob Johnson" />
                                    <Label styleClass="post-category lost-found" text="Lost &amp; Found" />
                                </HBox>
                                <Label styleClass="post-time" text="3 hours ago" />
                            </VBox>
                        </HBox>
                        <Label styleClass="post-content" text="Found a black wallet near the library. Please claim if it's yours!" wrapText="true" />
                        <HBox spacing="15" styleClass="post-actions-bar">
                            <Button fx:id="likeButton2" onAction="#handleLike" styleClass="action-btn like-btn" text="👍 Like" />
                            <Button styleClass="action-btn comment-btn" text="💬 Comment" />
                        </HBox>
                    </VBox>
                </VBox>
            </VBox>

            <!-- Right Sidebar - Routine Container -->
            <VBox prefWidth="300" spacing="10" styleClass="routine-container">
                <!-- Date Display -->
                <Label fx:id="dateLabel" styleClass="date-label" text="Sunday, 25 May, 2025" />

                <!-- Calendar Status -->
                <HBox alignment="CENTER" spacing="5">
                    <Label styleClass="status-title" text="Calendar Status" />
                    <Button styleClass="status-button" text="In progress" />
                </HBox>

                <!-- Class Routine -->
                <VBox spacing="0" styleClass="routine-box">
                    <padding>
                        <Insets bottom="10" left="10" right="10" top="10" />
                    </padding>

                    <Label styleClass="routine-title" text="Your Class Routine" />

                    <!-- Mathematics Class -->
                    <VBox spacing="5" styleClass="class-entry">
                        <padding>
                            <Insets bottom="5" left="0" right="0" top="5" />
                        </padding>
                        <Label styleClass="subject-label" text="Mathematics" />
                        <HBox spacing="5">
                            <Label styleClass="time-label" text="9:00 AM - 10:30 AM" />
                            <Button styleClass="section-button" text="Section A" />
                            <Button styleClass="room-button" text="Room 101" />
                        </HBox>
                    </VBox>

                    <!-- Physics Class -->
                    <VBox spacing="5" styleClass="class-entry">
                        <padding>
                            <Insets bottom="5" left="0" right="0" top="5" />
                        </padding>
                        <Label styleClass="subject-label" text="Physics" />
                        <HBox spacing="5">
                            <Label styleClass="time-label" text="11:00 AM - 12:30 PM" />
                            <Button styleClass="section-button" text="Section B" />
                            <Button styleClass="room-button" text="Room 203" />
                        </HBox>
                    </VBox>

                    <!-- Chemistry Class -->
                    <VBox spacing="5" styleClass="class-entry">
                        <padding>
                            <Insets bottom="5" left="0" right="0" top="5" />
                        </padding>
                        <Label styleClass="subject-label" text="Chemistry" />
                        <HBox spacing="5">
                            <Label styleClass="time-label" text="2:00 PM - 3:30 PM" />
                            <Button styleClass="section-button" text="Section A" />
                            <Button styleClass="room-button" text="Room 305" />
                        </HBox>
                    </VBox>

                    <!-- English Class -->
                    <VBox spacing="5" styleClass="class-entry">
                        <padding>
                            <Insets bottom="5" left="0" right="0" top="5" />
                        </padding>
                        <Label styleClass="subject-label" text="English" />
                        <HBox spacing="5">
                            <Label styleClass="time-label" text="4:00 PM - 5:30 PM" />
                            <Button styleClass="section-button" text="Section C" />
                            <Button styleClass="room-button" text="Room 112" />
                        </HBox>
                    </VBox>

                    <!-- Computer Science Class -->
                    <VBox spacing="5" styleClass="class-entry">
                        <padding>
                            <Insets bottom="5" left="0" right="0" top="5" />
                        </padding>
                        <Label styleClass="subject-label" text="Computer Science" />
                        <HBox spacing="5">
                            <Label styleClass="time-label" text="6:00 PM - 7:30 PM" />
                            <Button styleClass="section-button" text="Section A" />
                            <Button styleClass="room-button" text="Room 401" />
                        </HBox>
                    </VBox>
                </VBox>
            </VBox>
        </HBox>
    </center>
</BorderPane>
