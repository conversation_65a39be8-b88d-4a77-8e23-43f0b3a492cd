<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.shape.Circle?>
<?import javafx.geometry.Insets?>

<BorderPane fx:id="dashboardContainer" stylesheets="@dashboard.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1"
          fx:controller="com.example.uniconnect.DashboardController" styleClass="root" prefWidth="1200" prefHeight="800">

    <!-- Top Header - Matching DBMS Design -->
    <top>
        <HBox styleClass="header" spacing="15">
            <Label fx:id="greetingLabel" text="Good morning, Thomas" styleClass="greeting"/>
            <Region HBox.hgrow="ALWAYS"/>
            <HBox styleClass="notification" spacing="15">
                <Button styleClass="nav-icon" text="💬"/>
                <Button styleClass="nav-icon" text="🔔"/>
                <Circle radius="18" styleClass="profile-circle-header"/>
            </HBox>
        </HBox>
    </top>

    <!-- Left Sidebar - Matching DBMS Design -->
    <left>
        <VBox styleClass="sidebar" prefWidth="205">
            <!-- Logo Section -->
            <VBox styleClass="logo-section">
                <Label text="Uniconnect" styleClass="logo-text"/>
            </VBox>

            <!-- Navigation Menu -->
            <VBox styleClass="nav-menu" spacing="0">
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="👤" styleClass="sidebar-icon"/>
                            <Label text="Profile" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item active">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="🏠" styleClass="sidebar-icon"/>
                            <Label text="Home" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="👥" styleClass="sidebar-icon"/>
                            <Label text="Study Group" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="📝" styleClass="sidebar-icon"/>
                            <Label text="Notes" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="🔧" styleClass="sidebar-icon"/>
                            <Label text="Academic Tools" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
            </VBox>

            <Region VBox.vgrow="ALWAYS"/>

            <!-- Bottom Menu -->
            <VBox styleClass="bottom-menu" spacing="0">
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="⚙️" styleClass="sidebar-icon"/>
                            <Label text="Settings" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
            </VBox>
        </VBox>
    </left>

    <!-- Center Content - Matching DBMS Layout -->
    <center>
        <HBox spacing="20">
            <padding>
                <Insets top="20" right="20" bottom="20" left="20"/>
            </padding>

            <!-- Main Feed Area -->
            <VBox styleClass="main-content" spacing="20" prefWidth="500" HBox.hgrow="ALWAYS">
                <!-- Post Input Card -->
                <VBox styleClass="post-input-card" spacing="15">
                    <TextArea fx:id="postTextArea" promptText="What's on your mind?" styleClass="post-input" prefHeight="50"/>
                    <HBox styleClass="post-actions" spacing="10">
                        <ComboBox fx:id="categoryComboBox" promptText="Select Category" styleClass="category-select" prefWidth="140"/>
                        <Button styleClass="photo-btn" text="📷 Photo"/>
                        <Region HBox.hgrow="ALWAYS"/>
                        <Button fx:id="postButton" styleClass="post-btn" text="Post" onAction="#handlePost"/>
                    </HBox>
                </VBox>

                <!-- Posts Feed -->
                <VBox spacing="0">
                    <Label text="Recent Posts" styleClass="section-title"/>

                    <VBox styleClass="posts-container" spacing="0">
                        <!-- Alice Smith Post -->
                        <VBox styleClass="post-card" spacing="12">
                            <HBox spacing="12" alignment="CENTER_LEFT">
                                <Label text="Alice Smith" styleClass="post-author"/>
                                <Label text="Academic" styleClass="post-category"/>
                                <Region HBox.hgrow="ALWAYS"/>
                                <Label text="2 hours ago" styleClass="post-time"/>
                            </HBox>
                            <Label text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" styleClass="post-content"/>
                            <HBox spacing="15" styleClass="post-actions-bar">
                                <Button fx:id="likeButton1" text="👍 Like" styleClass="action-btn like-btn" onAction="#handleLike"/>
                                <Button text="💬 Comment" styleClass="action-btn comment-btn"/>
                            </HBox>
                        </VBox>

                        <!-- Bob Johnson Post -->
                        <VBox styleClass="post-card" spacing="12">
                            <HBox spacing="12" alignment="CENTER_LEFT">
                                <Label text="Bob Johnson" styleClass="post-author"/>
                                <Label text="Lost &amp; Found" styleClass="post-category lost-found"/>
                                <Region HBox.hgrow="ALWAYS"/>
                                <Label text="3 hours ago" styleClass="post-time"/>
                            </HBox>
                            <Label text="Found a black wallet near the library. Please claim if it's yours!" wrapText="true" styleClass="post-content"/>
                            <HBox spacing="15" styleClass="post-actions-bar">
                                <Button fx:id="likeButton2" text="👍 Like" styleClass="action-btn like-btn" onAction="#handleLike"/>
                                <Button text="💬 Comment" styleClass="action-btn comment-btn"/>
                            </HBox>
                        </VBox>
                    </VBox>
                </VBox>
            </VBox>

            <!-- Right Sidebar - Matching DBMS Design -->
            <VBox styleClass="right-sidebar" spacing="0" prefWidth="300">
                <!-- Date Header -->
                <VBox styleClass="date-section">
                    <Label fx:id="dateLabel" text="Friday, 25 April, 2025" styleClass="date-header"/>
                    <VBox spacing="8">
                        <Label text="Calendar Status" styleClass="status-label"/>
                        <Label text="In progress" styleClass="status-value"/>
                    </VBox>
                </VBox>

                <!-- Class Routine Card -->
                <VBox styleClass="routine-card">
                    <Label text="Your Class Routine" styleClass="routine-header"/>

                    <VBox styleClass="classes-list" spacing="0">
                        <!-- Mathematics Class -->
                        <VBox styleClass="class-entry">
                            <Label text="Mathematics" styleClass="subject-name"/>
                            <HBox spacing="8" alignment="CENTER_LEFT">
                                <Label text="🕒" styleClass="time-icon"/>
                                <Label text="9:00 AM - 10:30 AM" styleClass="class-time"/>
                            </HBox>
                            <HBox spacing="8" styleClass="class-meta">
                                <Label text="Section A" styleClass="section-label"/>
                                <Label text="Room 101" styleClass="room-label"/>
                            </HBox>
                        </VBox>

                        <!-- Physics Class -->
                        <VBox styleClass="class-entry">
                            <Label text="Physics" styleClass="subject-name"/>
                            <HBox spacing="8" alignment="CENTER_LEFT">
                                <Label text="🕒" styleClass="time-icon"/>
                                <Label text="11:00 AM - 12:30 PM" styleClass="class-time"/>
                            </HBox>
                            <HBox spacing="8" styleClass="class-meta">
                                <Label text="Section B" styleClass="section-label"/>
                                <Label text="Room 203" styleClass="room-label"/>
                            </HBox>
                        </VBox>

                        <!-- Chemistry Class -->
                        <VBox styleClass="class-entry">
                            <Label text="Chemistry" styleClass="subject-name"/>
                            <HBox spacing="8" alignment="CENTER_LEFT">
                                <Label text="🕒" styleClass="time-icon"/>
                                <Label text="2:00 PM - 3:30 PM" styleClass="class-time"/>
                            </HBox>
                            <HBox spacing="8" styleClass="class-meta">
                                <Label text="Section A" styleClass="section-label"/>
                                <Label text="Room 305" styleClass="room-label"/>
                            </HBox>
                        </VBox>

                        <!-- English Class -->
                        <VBox styleClass="class-entry">
                            <Label text="English" styleClass="subject-name"/>
                            <HBox spacing="8" alignment="CENTER_LEFT">
                                <Label text="🕒" styleClass="time-icon"/>
                                <Label text="4:00 PM - 5:30 PM" styleClass="class-time"/>
                            </HBox>
                            <HBox spacing="8" styleClass="class-meta">
                                <Label text="Section C" styleClass="section-label"/>
                                <Label text="Room 112" styleClass="room-label"/>
                            </HBox>
                        </VBox>

                        <!-- Computer Science Class -->
                        <VBox styleClass="class-entry">
                            <Label text="Computer Science" styleClass="subject-name"/>
                            <HBox spacing="8" alignment="CENTER_LEFT">
                                <Label text="🕒" styleClass="time-icon"/>
                                <Label text="6:00 PM - 7:30 PM" styleClass="class-time"/>
                            </HBox>
                            <HBox spacing="8" styleClass="class-meta">
                                <Label text="Section A" styleClass="section-label"/>
                                <Label text="Room 401" styleClass="room-label"/>
                            </HBox>
                        </VBox>
                    </VBox>
                </VBox>
            </VBox>
        </HBox>
    </center>
</BorderPane>