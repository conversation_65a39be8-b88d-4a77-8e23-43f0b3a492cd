<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.TextArea?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.Hyperlink?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.shape.Circle?>
<?import javafx.geometry.Insets?>

<BorderPane fx:id="dashboardContainer" stylesheets="@dashboard.css" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1"
          fx:controller="com.example.uniconnect.DashboardController" styleClass="root" prefWidth="1200" prefHeight="800">

    <!-- Top Header - Matching DBMS Design -->
    <top>
        <HBox styleClass="header" spacing="15">
            <Label fx:id="greetingLabel" text="Good morning, Thomas" styleClass="greeting"/>
            <Region HBox.hgrow="ALWAYS"/>
            <HBox styleClass="notification" spacing="15">
                <Button styleClass="nav-icon" text="💬"/>
                <Button styleClass="nav-icon" text="🔔"/>
                <Circle radius="18" styleClass="profile-circle-header"/>
            </HBox>
        </HBox>
    </top>

    <!-- Left Sidebar - Matching DBMS Design -->
    <left>
        <VBox styleClass="sidebar" prefWidth="205">
            <!-- Logo Section -->
            <VBox styleClass="logo-section">
                <Label text="Uniconnect" styleClass="logo-text"/>
            </VBox>

            <!-- Navigation Menu -->
            <VBox styleClass="nav-menu" spacing="0">
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="👤" styleClass="sidebar-icon"/>
                            <Label text="Profile" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item active">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="🏠" styleClass="sidebar-icon"/>
                            <Label text="Home" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="👥" styleClass="sidebar-icon"/>
                            <Label text="Study Group" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="📝" styleClass="sidebar-icon"/>
                            <Label text="Notes" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="🔧" styleClass="sidebar-icon"/>
                            <Label text="Academic Tools" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
            </VBox>

            <Region VBox.vgrow="ALWAYS"/>

            <!-- Bottom Menu -->
            <VBox styleClass="bottom-menu" spacing="0">
                <Button styleClass="sidebar-item">
                    <graphic>
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Label text="⚙️" styleClass="sidebar-icon"/>
                            <Label text="Settings" styleClass="sidebar-text"/>
                        </HBox>
                    </graphic>
                </Button>
            </VBox>
        </VBox>
    </left>

    <!-- Center Content - Matching DBMS Layout -->
    <center>
        <HBox spacing="20">
            <padding>
                <Insets top="20" right="20" bottom="20" left="20"/>
            </padding>

            <!-- Main Feed Area -->
            <VBox styleClass="main-content" spacing="20" prefWidth="500" HBox.hgrow="ALWAYS">
                <!-- Post Input Card -->
                <VBox styleClass="post-input-card" spacing="15">
                    <HBox spacing="12" alignment="CENTER_LEFT">
                        <Circle radius="20" styleClass="profile-circle-input"/>
                        <TextArea fx:id="postTextArea" promptText="What's on your mind?" styleClass="post-input" prefHeight="50" HBox.hgrow="ALWAYS"/>
                    </HBox>
                    <HBox styleClass="post-actions" spacing="10">
                        <ComboBox fx:id="categoryComboBox" promptText="Select Category" styleClass="category-select" prefWidth="160"/>
                        <Button styleClass="photo-btn" text="📷 Photo"/>
                        <Region HBox.hgrow="ALWAYS"/>
                        <Button fx:id="postButton" styleClass="post-btn" text="Post" onAction="#handlePost"/>
                    </HBox>
                </VBox>

                <!-- Posts Feed -->
                <VBox spacing="15">
                    <Label text="Recent Posts" styleClass="section-title"/>

                    <!-- Alice Smith Post -->
                    <VBox styleClass="post-card-individual" spacing="12">
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Circle radius="18" styleClass="profile-circle-post"/>
                            <VBox spacing="2" HBox.hgrow="ALWAYS">
                                <HBox spacing="8" alignment="CENTER_LEFT">
                                    <Label text="Alice Smith" styleClass="post-author"/>
                                    <Label text="Academic" styleClass="post-category"/>
                                </HBox>
                                <Label text="2 hours ago" styleClass="post-time"/>
                            </VBox>
                        </HBox>
                        <Label text="Looking for CS101 notes for this trimester. Can anyone share?" wrapText="true" styleClass="post-content"/>
                        <HBox spacing="15" styleClass="post-actions-bar">
                            <Button fx:id="likeButton1" text="👍 Like" styleClass="action-btn like-btn" onAction="#handleLike"/>
                            <Button text="💬 Comment" styleClass="action-btn comment-btn"/>
                        </HBox>
                    </VBox>

                    <!-- Bob Johnson Post -->
                    <VBox styleClass="post-card-individual" spacing="12">
                        <HBox spacing="12" alignment="CENTER_LEFT">
                            <Circle radius="18" styleClass="profile-circle-post"/>
                            <VBox spacing="2" HBox.hgrow="ALWAYS">
                                <HBox spacing="8" alignment="CENTER_LEFT">
                                    <Label text="Bob Johnson" styleClass="post-author"/>
                                    <Label text="Lost &amp; Found" styleClass="post-category lost-found"/>
                                </HBox>
                                <Label text="3 hours ago" styleClass="post-time"/>
                            </VBox>
                        </HBox>
                        <Label text="Found a black wallet near the library. Please claim if it's yours!" wrapText="true" styleClass="post-content"/>
                        <HBox spacing="15" styleClass="post-actions-bar">
                            <Button fx:id="likeButton2" text="👍 Like" styleClass="action-btn like-btn" onAction="#handleLike"/>
                            <Button text="💬 Comment" styleClass="action-btn comment-btn"/>
                        </HBox>
                    </VBox>
                </VBox>
            </VBox>

            <!-- Right Sidebar - Routine Container -->
            <VBox styleClass="routine-container" spacing="10" prefWidth="300">
                <!-- Date Display -->
                <Label fx:id="dateLabel" text="Sunday, 25 May, 2025" styleClass="date-label"/>

                <!-- Calendar Status -->
                <HBox spacing="5" alignment="CENTER">
                    <Label text="Calendar Status" styleClass="status-title"/>
                    <Button text="In progress" styleClass="status-button"/>
                </HBox>

                <!-- Class Routine -->
                <VBox styleClass="routine-box" spacing="0">
                    <padding>
                        <Insets top="10" right="10" bottom="10" left="10"/>
                    </padding>

                    <Label text="Your Class Routine" styleClass="routine-title"/>

                    <!-- Mathematics Class -->
                    <VBox styleClass="class-entry" spacing="5">
                        <padding>
                            <Insets top="5" right="0" bottom="5" left="0"/>
                        </padding>
                        <Label text="Mathematics" styleClass="subject-label"/>
                        <HBox spacing="5">
                            <Label text="9:00 AM - 10:30 AM" styleClass="time-label"/>
                            <Button text="Section A" styleClass="section-button"/>
                            <Button text="Room 101" styleClass="room-button"/>
                        </HBox>
                    </VBox>

                    <!-- Physics Class -->
                    <VBox styleClass="class-entry" spacing="5">
                        <padding>
                            <Insets top="5" right="0" bottom="5" left="0"/>
                        </padding>
                        <Label text="Physics" styleClass="subject-label"/>
                        <HBox spacing="5">
                            <Label text="11:00 AM - 12:30 PM" styleClass="time-label"/>
                            <Button text="Section B" styleClass="section-button"/>
                            <Button text="Room 203" styleClass="room-button"/>
                        </HBox>
                    </VBox>

                    <!-- Chemistry Class -->
                    <VBox styleClass="class-entry" spacing="5">
                        <padding>
                            <Insets top="5" right="0" bottom="5" left="0"/>
                        </padding>
                        <Label text="Chemistry" styleClass="subject-label"/>
                        <HBox spacing="5">
                            <Label text="2:00 PM - 3:30 PM" styleClass="time-label"/>
                            <Button text="Section A" styleClass="section-button"/>
                            <Button text="Room 305" styleClass="room-button"/>
                        </HBox>
                    </VBox>

                    <!-- English Class -->
                    <VBox styleClass="class-entry" spacing="5">
                        <padding>
                            <Insets top="5" right="0" bottom="5" left="0"/>
                        </padding>
                        <Label text="English" styleClass="subject-label"/>
                        <HBox spacing="5">
                            <Label text="4:00 PM - 5:30 PM" styleClass="time-label"/>
                            <Button text="Section C" styleClass="section-button"/>
                            <Button text="Room 112" styleClass="room-button"/>
                        </HBox>
                    </VBox>

                    <!-- Computer Science Class -->
                    <VBox styleClass="class-entry" spacing="5">
                        <padding>
                            <Insets top="5" right="0" bottom="5" left="0"/>
                        </padding>
                        <Label text="Computer Science" styleClass="subject-label"/>
                        <HBox spacing="5">
                            <Label text="6:00 PM - 7:30 PM" styleClass="time-label"/>
                            <Button text="Section A" styleClass="section-button"/>
                            <Button text="Room 401" styleClass="room-button"/>
                        </HBox>
                    </VBox>
                </VBox>
            </VBox>
        </HBox>
    </center>
</BorderPane>